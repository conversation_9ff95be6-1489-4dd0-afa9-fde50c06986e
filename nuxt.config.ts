// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-07-15",
  future: { compatibilityVersion: 4 },
  devtools: { enabled: true },

  modules: ['@nuxt/ui-pro'],
  css: ['~/assets/css/main.css'],
  uiPro: {
    license: process.env.NUXT_UI_PRO_LICENSE || process.env.MY_ENVIRONMENT_VARIABLE
  },
  ui: {
    prefix: 'U',           // เปลี่ยนได้
    fonts: true,           // เปิด/ปิด @nuxt/fonts
    colorMode: true,       // เปิด/ปิด @nuxt/color-mode
    theme: {
      colors: ['primary', 'error'], // เลือก alias สีที่จะมี
      transitions: false            // ปิดเอฟเฟ็กต์โฮเวอร์
    }
  },

  runtimeConfig: {
    apiSecret: process.env.API_SECRET,
    public: {
      apiBase: process.env.Nuxt_PUBLIC_API_BASE || "/api",
    },
  },

  app: {
    head: {
      titleTemplate: (title?: string) => (title ? `${title} | MyApp` : 'MyApp'),
      meta: [{ name: 'viewport', content: 'width=device-width, initial-scale=1' }]
    }
  },

  typescript: { strict: true },

  // ตั้งค่า i18n
  i18n: {
    lazy: true,
    langDir: 'lang',
    defaultLocale: 'th',
    strategy: 'prefix_except_default',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root' 
    },
    locales: [
      { code: 'th', name: 'ไทย',  file: 'th.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ]
  }
});
