// server/api/vehicles/index.get.ts
type Vehicle = { id: number; brand: string; model: string; license: string; province: string }

const MOCK: Vehicle[] = [
  { id: 1, brand: 'TOYOTA', model: 'HILUX',  license: 'บย 8846', province: 'ชลบุรี' },
  { id: 2, brand: 'NISSAN', model: 'NAVARA', license: 'งย 5356', province: 'ชลบุรี' },
  { id: 3, brand: 'HONDA',  model: 'CITY',   license: 'กข 1234', province: 'กรุงเทพมหานคร' },
  // ...เติมข้อมูลจริงของคุณภายหลัง
]

export default defineEventHandler((event) => {
  const q = String(getQuery(event).q ?? '')
  const brand = getQuery(event).brand ? String(getQuery(event).brand) : ''
  const province = getQuery(event).province ? String(getQuery(event).province) : ''
  const sortByRaw = String(getQuery(event).sortBy ?? 'brand')
  const page = Math.max(1, parseInt(String(getQuery(event).page ?? '1')))
  const pageSize = Math.max(1, parseInt(String(getQuery(event).pageSize ?? '20')))

  const sortKey = (['brand','model','license','province'] as const)
    .includes(sortByRaw as any) ? sortByRaw as 'brand'|'model'|'license'|'province' : 'brand'

  let list = [...MOCK]

  if (q) {
    const s = q.toLowerCase()
    list = list.filter(v =>
      [v.brand, v.model, v.license, v.province]
        .some(x => x.toLowerCase().includes(s))
    )
  }
  if (brand)    list = list.filter(v => v.brand === brand)
  if (province) list = list.filter(v => v.province === province)

  list.sort((a, b) => String(a[sortKey]).localeCompare(String(b[sortKey])))

  const total = list.length
  const start = (page - 1) * pageSize
  const items = list.slice(start, start + pageSize)

  return { items, total, page, pageSize }
})
