// composables/useVehicleFilter.ts
type Vehicle = {
  id: number
  brand: string
  model: string
  license: string
  province: string
  color?: string
}

export const useVehicleFilter = (all: Ref<Vehicle[]>) => {
  const q = ref('')
  const brand = ref<string | null>(null)
  const province = ref<string | null>(null)
  const sortBy = ref<'brand' | 'model' | 'license' | 'province'>('brand')

  const uniqueBrands = computed(() =>
    Array.from(new Set(all.value.map(v => v.brand))).sort()
  )
  const uniqueProvinces = computed(() =>
    Array.from(new Set(all.value.map(v => v.province))).sort()
  )

  const filtered = computed(() => {
    let list = all.value

    if (q.value) {
      const s = q.value.toLowerCase()
      list = list.filter(v =>
        [v.brand, v.model, v.license, v.province, v.color ?? '']
          .some(x => x.toLowerCase().includes(s))
      )
    }
    if (brand.value)    list = list.filter(v => v.brand === brand.value)
    if (province.value) list = list.filter(v => v.province === province.value)

    return [...list].sort((a, b) =>
      String(a[sortBy.value]).localeCompare(String(b[sortBy.value]))
    )
  })

  const reset = () => {
    q.value = ''
    brand.value = null
    province.value = null
    sortBy.value = 'brand'
  }

  return { q, brand, province, sortBy, uniqueBrands, uniqueProvinces, filtered, reset }
}
