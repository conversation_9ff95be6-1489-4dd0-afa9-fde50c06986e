<script setup lang="ts">
/**
 * Vehicles Index (Nuxt v4 + Nuxt UI Pro + i18n)
 * - ดึงข้อมูลจาก /vehicles
 * - ตัวกรอง/ค้นหาแบบ client-side (เร็วและใช้งานง่าย)
 * - ข้อความทั้งหมดใช้ i18n (ไทย–อังกฤษ)
 */

useHead({ title: 'Vehicles' })

// ---------- i18n ----------
const { t, n } = useI18n()

// ---------- types ----------
type Vehicle = { id: number; brand: string; model: string; license: string; province: string }
type ListRes = { items: Vehicle[]; total: number }

// ---------- data ----------
const api = useApi()
const { data, pending, error, refresh } = await useAsyncData('vehicles', () =>
  api.get<ListRes>('/vehicles')
)

// all = รายการทั้งหมดจาก API (ยังโหลดให้เป็น [])
const all = computed<Vehicle[]>(() => data.value?.items ?? [])

// ---------- ฟิลเตอร์แบบ client-side ----------
const { q, brand, province, sortBy, uniqueBrands, uniqueProvinces, filtered, reset } =
  useVehicleFilter(all)

// ป้าย "ทุกยี่ห้อ/ทุกจังหวัด" ต้องเป็น reactive ตามภาษา
const brandAllLabel = computed(() => t('app.allBrands'))
const provinceAllLabel = computed(() => t('app.allProvinces'))

// ค่าที่ผูกกับ USelect (string) แล้ว map -> composable (null = ทั้งหมด)
const brandRaw = ref<string>(brandAllLabel.value)
const provinceRaw = ref<string>(provinceAllLabel.value)

watch(brandRaw, v => (brand.value = v === brandAllLabel.value ? null : v))
watch(provinceRaw, v => (province.value = v === provinceAllLabel.value ? null : v))

// อัปเดตค่าปัจจุบันเมื่อสลับภาษา (ถ้ากำลังเลือก "ทั้งหมด")
watch([brandAllLabel, provinceAllLabel], () => {
  if (brand.value === null) brandRaw.value = brandAllLabel.value
  if (province.value === null) provinceRaw.value = provinceAllLabel.value
})

// options ของ USelect
const brandOptions = computed(() => [brandAllLabel.value, ...uniqueBrands.value])
const provinceOptions = computed(() => [provinceAllLabel.value, ...uniqueProvinces.value])

// sort options ให้แปล label แต่เก็บ value เป็นคีย์จริง
const sortOptions = computed(() => ([
  { label: t('app.brand'),    value: 'brand'    },
  { label: t('app.sortBy') + ' - Model', value: 'model'    },
  { label: t('app.sortBy') + ' - License', value: 'license'  },
  { label: t('app.province'), value: 'province' }
]))

// USelect ต้องการ primitive หรือกำหนด :option-attribute
const sortValue = computed({
  get: () => sortBy.value,
  set: (v: 'brand'|'model'|'license'|'province') => (sortBy.value = v)
})
</script>

<template>
  <UContainer class="py-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between gap-3">
      <div>
        <h1 class="text-2xl font-semibold">{{ t('app.vehicles') }}</h1>
        <p class="text-sm opacity-70">
          {{ t('app.title') }}
        </p>
      </div>

      <div class="flex gap-2">
        <UButton icon="i-lucide-rotate-ccw" variant="ghost" @click="reset">
          {{ t('app.reset') }}
        </UButton>
        <UButton icon="i-lucide-refresh-cw" variant="soft" @click="refresh">
          {{ t('app.refresh') }}
        </UButton>
      </div>
    </div>

    <!-- Filter Card -->
    <UCard>
      <template #header>{{ t('app.filters') }}</template>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
        <UInput v-model="q" :placeholder="t('app.searchPlaceholder')" />

        <USelect v-model="brandRaw" :options="brandOptions" />

        <USelect v-model="provinceRaw" :options="provinceOptions" />

        <!-- USelect กับ object options -> ใช้ option-attribute ระบุฟิลด์ -->
        <USelect
          v-model="sortValue"
          :options="sortOptions"
          option-attribute="label"
          value-attribute="value"
        />
      </div>

      <template #footer>
        <div class="text-sm opacity-70">
          {{ t('app.showing', { count: n(filtered.length), total: n(all.length) }) }}
        </div>
      </template>
    </UCard>

    <!-- Loading / Error -->
    <div v-if="pending" class="flex items-center gap-2 opacity-70">
      <UIcon name="i-lucide-loader" class="animate-spin" />
      <span>Loading…</span>
    </div>

    <UCard v-else-if="error" class="border-red-300/60 bg-red-50 dark:bg-red-950/30">
      <div class="flex items-center gap-2 text-red-700 dark:text-red-300">
        <UIcon name="i-lucide-circle-alert" />
        <span>Load failed: {{ error.message }}</span>
      </div>
    </UCard>

    <!-- Table / Empty -->
    <UCard v-else>
      <template v-if="filtered.length > 0">
        <div class="overflow-x-auto">
          <table class="min-w-full text-sm">
            <thead class="bg-gray-50 dark:bg-gray-900/40">
              <tr>
                <th class="px-3 py-2 text-left font-medium">ID</th>
                <th class="px-3 py-2 text-left font-medium">Brand / Model</th>
                <th class="px-3 py-2 text-left font-medium">License</th>
                <th class="px-3 py-2 text-left font-medium">Province</th>
                <th class="px-3 py-2"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="v in filtered" :key="v.id" class="border-t">
                <td class="px-3 py-2">{{ v.id }}</td>
                <td class="px-3 py-2">
                  <div class="flex items-center gap-2">
                    <UBadge color="primary" variant="soft">{{ v.brand }}</UBadge>
                    <span>{{ v.model }}</span>
                  </div>
                </td>
                <td class="px-3 py-2 font-medium">{{ v.license }}</td>
                <td class="px-3 py-2">{{ v.province }}</td>
                <td class="px-3 py-2 text-right">
                  <UButton
                    :to="`/vehicles/${v.id}`"
                    size="xs"
                    icon="i-lucide-external-link"
                    variant="ghost"
                  >
                    {{ t('app.details') }}
                  </UButton>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>

      <template v-else>
        <div class="flex items-center gap-3 py-8 justify-center opacity-70">
          <UIcon name="i-lucide-search-x" />
          <span>ไม่พบรายการที่ตรงกับตัวกรอง</span>
        </div>
      </template>
    </UCard>
  </UContainer>
</template>

<style scoped>
/* ปรับ spacing ของ cell ตารางเล็กน้อยบนมือถือ */
@media (max-width: 480px) {
  th, td { padding: .5rem .5rem; }
}
</style>
