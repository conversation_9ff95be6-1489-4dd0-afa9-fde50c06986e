<script setup lang="ts">
const { t } = useI18n()
</script>

<template>
  <div>
    <header class="p-4 border-b flex items-center justify-between">
      <nav class="flex gap-3">
        <NuxtLink to="/">{{ t('app.home') }}</NuxtLink>
        <NuxtLink to="/vehicles">{{ t('app.vehicles') }}</NuxtLink>
        <NuxtLink to="/about">{{ t('app.about') }}</NuxtLink>
      </nav>
      <LocaleSwitcher />
    </header>

    <main class="p-6">
      <slot />
    </main>
  </div>
</template>
