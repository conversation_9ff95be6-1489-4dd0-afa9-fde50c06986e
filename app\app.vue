<script setup lang="ts">
const { locale } = useI18n()

// โหลด locale ของ Nuxt UI (Pro) ตามภาษาปัจจุบัน
const uiLocale = computed(async () => {
  if (locale.value === 'th') {
    const { default: th } = await import('@nuxt/ui-pro/locale/th') // ถ้าไม่มี th ให้ใช้ en เป็นค่า fallback
    return th
  } else {
    const { default: en } = await import('@nuxt/ui-pro/locale/en')
    return en
  }
})
</script>

<template>
  <!-- ส่ง locale ให้ UApp เพื่อให้คอมโพเนนต์ UI แปลข้อความและฟอร์แมต -->
  <UApp :locale="uiLocale">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </UApp>
</template>
