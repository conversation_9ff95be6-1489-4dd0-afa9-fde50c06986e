<script setup lang="ts">
const { locale, locales, setLocale } = useI18n()

const items = computed(() =>
  locales.value.map(l => ({
    label: typeof l === 'string' ? l : (l as any).name,
    code: typeof l === 'string' ? l : (l as any).code
  }))
)

const current = computed(() => items.value.find(i => i.code === locale.value))
const switchTo = (code: string) => setLocale(code)
</script>

<template>
  <UDropdown :items="[items]">
    <UButton
      color="gray"
      variant="ghost"
      icon="i-lucide-globe"
      :label="current?.label || 'Lang'"
    />
    <template #item="{ item }">
      <button class="w-full text-left px-3 py-1.5" @click="switchTo(item.code)">
        {{ item.label }}
      </button>
    </template>
  </UDropdown>
</template>
